"use client";
import React, { useEffect, useState } from "react";
import Logo from "../../assets/images/logo 1.png";
import Image from "next/image";
import Button from "../UI/Button/Button";
import { useCookies } from "next-client-cookies";
import { useRouter } from "next/navigation";
import { setCookie } from "cookies-next";
import { BiHeart, BiX } from "react-icons/bi";

function RandomMatchesPopup() {
  const cookies = useCookies();
  const router = useRouter();
  const [showPopup, setShowPopup] = useState(false);

  // Messages to show randomly
  const messages = [
    "<PERSON><PERSON> got pumped by <PERSON><PERSON><PERSON> in the dressing room at Woolies while trying on a skirt, you can get pumped pretty much anywhere.",
    "<PERSON><PERSON> is Pumping <PERSON><PERSON> for <PERSON><PERSON> and wena dololo! Where is your boyfriend? You are sitting at home, you are so lonelyyy...",
  ];

  // Randomly select a message
  const [message, setMessage] = useState("");

  useEffect(() => {
    // Check if user is logged in by checking for userData cookie
    const userData = cookies.get("userData");
    if (!userData || userData === "undefined") {
      return; // Don't show popup if user is not logged in
    }

    // Get the login timestamp
    const loginTime = cookies.get("loginTimestamp");
    if (!loginTime) {
      return; // No login timestamp available
    }

    // Cleanup old popup session cookies (older than 24 hours)
    const cleanupOldCookies = () => {
      const now = new Date().getTime();
      const oneDayInMs = 24 * 60 * 60 * 1000;

      // This is a simple cleanup - in a real app you might want to store session IDs differently
      // For now, we'll just clean up based on the current login timestamp
      const currentLoginTimestamp = parseInt(loginTime);
      if (now - currentLoginTimestamp > oneDayInMs) {
        // If current login is older than 24 hours, it's likely stale
        setCookie("loginTimestamp", "");
        return;
      }
    };

    cleanupOldCookies();

    const loginTimestamp = parseInt(loginTime);
    const now = new Date().getTime();
    const twoMinutesInMs = 2 * 60 * 1000; // 2 minutes = 120,000 ms
    const timeSinceLogin = now - loginTimestamp;

    // Check if this login session has already shown the popup
    const popupShownForSession = cookies.get(`popupShown_${loginTimestamp}`);
    if (popupShownForSession) {
      return; // Popup already shown for this login session
    }

    // If exactly 2 minutes or more have passed since login, show the popup
    if (timeSinceLogin >= twoMinutesInMs) {
      // Select a random message
      const randomMessage =
        messages[Math.floor(Math.random() * messages.length)];
      setMessage(randomMessage);
      setShowPopup(true);

      // Mark that popup has been shown for this login session
      setCookie(`popupShown_${loginTimestamp}`, "true");

      // Also update the general last shown time for any other logic that might need it
      setCookie("lastMatchesPopupShown", now.toString());
    } else {
      // Set a timeout to show the popup exactly at the 2-minute mark
      const timeUntilShow = twoMinutesInMs - timeSinceLogin;
      const timeoutId = setTimeout(() => {
        // Double-check that popup hasn't been shown yet and user is still logged in
        const currentUserData = cookies.get("userData");
        const currentPopupShown = cookies.get(`popupShown_${loginTimestamp}`);

        if (
          currentUserData &&
          currentUserData !== "undefined" &&
          !currentPopupShown
        ) {
          const randomMessage =
            messages[Math.floor(Math.random() * messages.length)];
          setMessage(randomMessage);
          setShowPopup(true);

          // Mark that popup has been shown for this login session
          setCookie(`popupShown_${loginTimestamp}`, "true");
          setCookie("lastMatchesPopupShown", new Date().getTime().toString());
        }
      }, timeUntilShow);

      // Cleanup timeout on component unmount
      return () => clearTimeout(timeoutId);
    }
  }, []);

  function closePopUp() {
    setShowPopup(false);
  }

  function goToMatches() {
    setShowPopup(false);
    router.push("../../matches");
  }

  // If popup shouldn't be shown, return null
  if (!showPopup) {
    return null;
  }

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
      <div className="bg-white flex-col flex items-center px-6 py-8 space-y-6 rounded-2xl shadow-sm max-w-md w-full mx-4 relative">
        <button
          onClick={closePopUp}
          className="absolute top-3 right-3 text-gray-500 hover:text-gray-700"
        >
          <BiX className="text-2xl" />
        </button>

        <Image
          src="/pumkin_random_logo.png"
          width={100}
          height={100}
          className="absolute -top-20"
        />

        <div className="flex items-center flex-col text-center">
          <p className="text-sm text-gray-700 mb-2">{message}</p>
          <p className="text-xs text-grey-300">Find Your Match!</p>
        </div>

        <div className="flex w-full">
          <Button label={"Pump!"} variant={"primary"} onClick={goToMatches} />
        </div>
      </div>
    </div>
  );
}

export default RandomMatchesPopup;
