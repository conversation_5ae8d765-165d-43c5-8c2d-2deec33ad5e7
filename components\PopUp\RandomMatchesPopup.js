"use client";
import React, { useEffect, useState } from "react";
import Logo from "../../assets/images/logo 1.png";
import Image from "next/image";
import Button from "../UI/Button/Button";
import { useCookies } from "next-client-cookies";
import { useRouter } from "next/navigation";
import { setCookie } from "cookies-next";
import { BiHeart, BiX } from "react-icons/bi";

function RandomMatchesPopup() {
  const cookies = useCookies();
  const router = useRouter();
  const [showPopup, setShowPopup] = useState(false);

  // Messages to show randomly
  const messages = [
    "<PERSON><PERSON> got pumped by <PERSON><PERSON><PERSON> in the dressing room at Woolies while trying on a skirt, you can get pumped pretty much anywhere.",
    "<PERSON><PERSON> is Pumping <PERSON><PERSON> for <PERSON><PERSON> and wena dololo! Where is your boyfriend? You are sitting at home, you are so lonelyyy...",
  ];

  // Randomly select a message
  const [message, setMessage] = useState("");

  useEffect(() => {
    // Get the last time the popup was shown
    const lastShown = cookies.get("lastMatchesPopupShown");
    const now = new Date().getTime();

    // Check if user is logged in by checking for userData cookie
    const userData = cookies.get("userData");
    if (!userData || userData === "undefined") {
      return; // Don't show popup if user is not logged in
    }

    // Get or set the login timestamp
    let loginTime = cookies.get("loginTimestamp");
    if (!loginTime) {
      // If no login timestamp exists, set it now (this handles existing logged-in users)
      setCookie("loginTimestamp", now.toString());
      return; // Don't show popup immediately after setting timestamp
    }

    const loginTimestamp = parseInt(loginTime);
    const twoMinutesInMs = 2 * 60 * 1000; // 2 minutes = 120,000 ms

    // Only proceed if at least 2 minutes have passed since login
    if (now - loginTimestamp < twoMinutesInMs) {
      return; // Not enough time has passed since login
    }

    // Only show popup if it hasn't been shown in the last 30 minutes (1800000 ms)
    // or if it's never been shown before
    if (!lastShown || now - parseInt(lastShown) > 1800000) {
      // Random chance (20%) to show the popup
      if (Math.random() < 0.2) {
        // Select a random message
        const randomMessage =
          messages[Math.floor(Math.random() * messages.length)];
        setMessage(randomMessage);
        setShowPopup(true);

        // Update the last shown time
        setCookie("lastMatchesPopupShown", now.toString());
      }
    }
  }, []);

  function closePopUp() {
    setShowPopup(false);
  }

  function goToMatches() {
    setShowPopup(false);
    router.push("../../matches");
  }

  // If popup shouldn't be shown, return null
  if (!showPopup) {
    return null;
  }

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
      <div className="bg-white flex-col flex items-center px-6 py-8 space-y-6 rounded-2xl shadow-sm max-w-md w-full mx-4 relative">
        <button
          onClick={closePopUp}
          className="absolute top-3 right-3 text-gray-500 hover:text-gray-700"
        >
          <BiX className="text-2xl" />
        </button>

        <Image
          src="/pumkin_random_logo.png"
          width={100}
          height={100}
          className="absolute -top-20"
        />

        <div className="flex items-center flex-col text-center">
          <p className="text-sm text-gray-700 mb-2">{message}</p>
          <p className="text-xs text-grey-300">Find Your Match!</p>
        </div>

        <div className="flex w-full">
          <Button label={"Pump!"} variant={"primary"} onClick={goToMatches} />
        </div>
      </div>
    </div>
  );
}

export default RandomMatchesPopup;
