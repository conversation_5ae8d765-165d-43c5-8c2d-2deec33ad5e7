import React from "react";
import UserImage from "../../../assets/images/User01.png";
import Image from "next/image";
import Button from "../Button/Button";
import { useRouter } from "next/navigation";
import { setCookie } from "cookies-next";
import { BiImageAdd, BiUser } from "react-icons/bi";

function ProfileCardMatches({
  id,
  username,
  email,
  coverPicture,
  name,
  surname = "",
  hickies,
  pumpkins,
  dob,
  city,
  country,
  largeFont = false, // NEW PROP
}) {
  function getSurnameInitials(surname) {
    const surnameWords = (surname || "").trim().split(" ");
    const initials = surnameWords.map((word) => word.charAt(0).toUpperCase());
    const initialsString = initials.join("");
    return initialsString;
  }

  const router = useRouter();
  function calculateAge(dateString) {
    const today = new Date();
    const birthDate = new Date(dateString);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < birthDate.getDate())
    ) {
      age--;
    }
    return age;
  }

  return (
    // OUTMOST DIV OF THE CARD:
    // - Adjusted max-w for 5% wider card.
    // - Keeps overflow-hidden for sharp clipping of rounded corners.
    // - Uses rounded-4xl for strong, consistent corner rounding.
    <div
      className="w-full max-w-[95vw] max-[375px]:max-w-[90vw] sm:max-w-[18.8rem] md:max-w-[14.1rem] lg:max-w-[24.875rem] xl:max-w-[24.875rem] mx-auto relative overflow-hidden rounded-4xl"
      onClick={() => {
        setCookie("selectedUserProfile", email);
        router.push(`../../user-profile`);
      }}
    >
      {/* IMAGE CONTAINER DIV: */}
      {/* - Set to aspect-[5/8] for the preferred card height. */}
      <div className="relative w-full max-[375px]:aspect-[2/3] aspect-[3/5] sm:aspect-[5/8]">
        {/* IMAGE COMPONENT: */}
        {/* - Its own rounding will be clipped by the parent's overflow-hidden. */}
        <Image
          src={
            coverPicture ||
            "https://firebasestorage.googleapis.com/v0/b/pumpkin-web.appspot.com/o/posts%2FCImkQo7VhL6HSz8pu4sc%2Fimage?alt=media&token=8cc0a660-ca8b-461c-bfd3-a324aeeec56c"
          }
          width={400}
          height={500}
          className="w-full h-full object-cover lg:rounded-3xl rounded-xl"
          alt="profile"
          unoptimized={true}
          priority={true}
          loader={({ src }) => src}
        />
        {/* GRADIENT OVERLAY: */}
        {/* - Now has matching bottom rounding (rounded-b-4xl) to blend with the card's shape. */}
        <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-black/80 to-transparent rounded-b-4xl"></div>
      </div>

      {/* USER INFO OVERLAY: */}
      {/* - Also has matching bottom rounding (rounded-b-4xl) to ensure consistent corners. */}
      <div className="absolute bottom-0 left-0 right-0 z-20 p-3 lg:p-4 rounded-b-4xl">
        <div className="text-white flex flex-col w-full space-y-1 lg:space-y-2">
        <div className="font-bold text-lg lg:text-base xl:text-lg line-clamp-1">
          {name} {surname} {calculateAge(dob)}
        </div>
        <div className="text-sm lg:text-xs xl:text-sm">
          @{username}
        </div>
        <div className="text-sm lg:text-ls xl:text-sm line-clamp-1">
         {pumpkins} Pumkins - {hickies} Hickies 
      </div>

          <div className="pt-1">
            <span className="bg-white px-2 py-1 rounded-lg text-xs text-blue-600 font-bold inline-block invisible">
              New
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ProfileCardMatches;