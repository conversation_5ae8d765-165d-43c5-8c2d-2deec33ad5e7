"use client";
import React, { useEffect, useState } from "react";
import Logo from "../../assets/images/logo 1.png";
import Image from "next/image";
import Button from "../UI/Button/Button";
import { useCookies } from "next-client-cookies";
import { useRouter, usePathname } from "next/navigation";
import { setCookie } from "cookies-next";

function WelcomeUserPopUp() {
  const cookies = useCookies();
  const router = useRouter();
  const pathname = usePathname();
  const [showPopup, setShowPopup] = useState(false);

  useEffect(() => {
    // Only show on profile route
    if (pathname !== "/profile") {
      setShowPopup(false);
      return;
    }

    // Check if user is logged in
    const userData = cookies.get("userData");
    if (!userData || userData === "undefined" || userData === "null") {
      setShowPopup(false);
      return;
    }

    // Check if welcome popup has already been shown for this user
    const welcomeShown = cookies.get("welcomePopupShown");
    if (welcomeShown === "true") {
      setShowPopup(false);
      return;
    }

    // Check if this is marked as a new user (set during login)
    const shouldShowWelcome = cookies.get("shouldShowWelcome");
    if (shouldShowWelcome === "true") {
      setShowPopup(true);
      setCookie("welcomePopupShown", "true");
      setCookie("shouldShowWelcome", ""); // Clear the flag
    }
  }, [pathname, cookies]);

  function closePopUp() {
    setShowPopup(false);
  }

  function goToMatches() {
    setShowPopup(false);
    router.push("../../matches");
  }

  // If popup shouldn't be shown, return null
  console.log("WelcomeUser render - showPopup:", showPopup);
  if (!showPopup) {
    console.log("WelcomeUser render - returning null (not showing popup)");
    return null;
  }

  console.log("WelcomeUser render - RENDERING POPUP!");

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
      <div className="bg-white flex-col flex items-center px-6 py-8 space-y-8 rounded-2xl shadow-sm max-w-md w-full mx-4">
        <div>
          <Image
            src={Logo}
            width={100}
            height={100}
            className=""
            alt="Pumpkin logo"
          />
        </div>
        <div className="flex items-center flex-col text-center">
          <p className="text-2xl font-bold">Welcome to Pumpkin!</p>
          <p className="text-sm text-gray-700 mt-2">
            Where true love meets fortune. We're excited to have you join our
            community!
          </p>
          <p className="text-sm text-gray-700 mt-4">
            Check out your potential matches and start connecting with people
            who share your interests.
          </p>
        </div>
        <div className="flex w-full flex-col space-y-3">
          <Button
            label={"Go to Matches"}
            variant={"primary"}
            onClick={goToMatches}
          />
          <Button label={"Close"} variant={"secondary"} onClick={closePopUp} />
        </div>
        <div className="text-xs text-gray-500">
          Welcome to your new journey!
        </div>
      </div>
    </div>
  );
}
export default WelcomeUserPopUp;
