"use client";
import React, { useEffect, useState } from "react";
import Logo from "../../assets/images/logo 1.png";
import Image from "next/image";
import Button from "../UI/Button/Button";
import { useCookies } from "next-client-cookies";
import { useRouter, usePathname } from "next/navigation";
import { setCookie } from "cookies-next";

function WelcomeUserPopUp() {
  const cookies = useCookies();
  const router = useRouter();
  const pathname = usePathname();
  const [showPopup, setShowPopup] = useState(false);

  useEffect(() => {
    console.log("WelcomeUser useEffect triggered");
    console.log("Current pathname:", pathname);

    // Only show on profile route
    if (pathname !== "/profile") {
      console.log("Not on profile route, hiding popup");
      setShowPopup(false);
      return;
    }

    // Check if user is logged in
    const userData = cookies.get("userData");
    console.log("userData cookie:", userData);
    if (!userData || userData === "undefined") {
      console.log("No valid userData, hiding popup");
      setShowPopup(false);
      return;
    }

    // Check if this user should see the welcome popup (set during login)
    const shouldShowWelcome = cookies.get("shouldShowWelcome");
    console.log("shouldShowWelcome cookie:", shouldShowWelcome);
    if (
      !shouldShowWelcome ||
      shouldShowWelcome === "false" ||
      shouldShowWelcome === ""
    ) {
      console.log("shouldShowWelcome is false/empty, hiding popup");
      setShowPopup(false);
      return;
    }

    // Check if welcome popup has already been shown for this user
    const welcomeShown = cookies.get("welcomePopupShown");
    console.log("welcomePopupShown cookie:", welcomeShown);
    if (welcomeShown && welcomeShown !== "false" && welcomeShown !== "") {
      console.log("Welcome popup already shown, hiding popup");
      setShowPopup(false);
      return;
    }

    // Show the popup and mark it as shown
    console.log("All conditions met, showing welcome popup");
    setShowPopup(true);
    setCookie("welcomePopupShown", "true");
    // Clear the shouldShowWelcome flag since we're showing it now
    setCookie("shouldShowWelcome", "");
  }, [pathname, cookies]);

  function closePopUp() {
    setShowPopup(false);
  }

  function goToMatches() {
    setShowPopup(false);
    router.push("../../matches");
  }

  // If popup shouldn't be shown, return null
  if (!showPopup) {
    return null;
  }

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
      <div className="bg-white flex-col flex items-center px-6 py-8 space-y-8 rounded-2xl shadow-sm max-w-md w-full mx-4">
        <div>
          <Image
            src={Logo}
            width={100}
            height={100}
            className=""
            alt="Pumpkin logo"
          />
        </div>
        <div className="flex items-center flex-col text-center">
          <p className="text-2xl font-bold">Welcome to Pumpkin!</p>
          <p className="text-sm text-gray-700 mt-2">
            Where true love meets fortune. We're excited to have you join our
            community!
          </p>
          <p className="text-sm text-gray-700 mt-4">
            Check out your potential matches and start connecting with people
            who share your interests.
          </p>
        </div>
        <div className="flex w-full flex-col space-y-3">
          <Button
            label={"Go to Matches"}
            variant={"primary"}
            onClick={goToMatches}
          />
          <Button label={"Close"} variant={"secondary"} onClick={closePopUp} />
        </div>
        <div className="text-xs text-gray-500">
          Welcome to your new journey!
        </div>
      </div>
    </div>
  );
}
export default WelcomeUserPopUp;
