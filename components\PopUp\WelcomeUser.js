"use client";
import React, { useEffect, useState } from "react";
import Logo from "../../assets/images/logo 1.png";
import Image from "next/image";
import Button from "../UI/Button/Button";
import { useCookies } from "next-client-cookies";
import { useRouter, usePathname } from "next/navigation";
import { setCookie } from "cookies-next";

function WelcomeUserPopUp() {
  const cookies = useCookies();
  const router = useRouter();
  const pathname = usePathname();
  const [showPopup, setShowPopup] = useState(false);

  console.log("WelcomeUserPopUp component mounted/rendered");

  // Simple useEffect that should always trigger
  useEffect(() => {
    console.log("Simple useEffect triggered on mount");
  }, []);

  useEffect(() => {
    console.log("=== WelcomeUser useEffect triggered ===");
    console.log("Current pathname:", pathname);
    console.log("All cookies:", document.cookie);

    // Simple test - always show popup on profile route for debugging
    if (pathname === "/profile") {
      console.log("On profile route - checking conditions...");

      // Check if user is logged in
      const userData = cookies.get("userData");
      console.log("userData cookie raw:", userData);

      if (!userData || userData === "undefined" || userData === "null") {
        console.log("No valid userData, hiding popup");
        setShowPopup(false);
        return;
      }

      // Check if welcome popup has already been shown
      const welcomeShown = cookies.get("welcomePopupShown");
      console.log("welcomePopupShown cookie:", welcomeShown);

      if (welcomeShown === "true") {
        console.log("Welcome popup already shown, hiding popup");
        setShowPopup(false);
        return;
      }

      // Parse user data to check if this is a new user
      try {
        const user = JSON.parse(userData);
        console.log("Parsed user data:", user);

        const isNewUser =
          !user.profilePicture ||
          user.profilePicture === "" ||
          !user.bio ||
          user.bio === "" ||
          !user.hobbies ||
          user.hobbies.length === 0 ||
          !user.passions ||
          user.passions.length === 0;

        console.log("Is new user check:", {
          profilePicture: user.profilePicture,
          bio: user.bio,
          hobbies: user.hobbies,
          passions: user.passions,
          isNewUser: isNewUser,
        });

        if (isNewUser) {
          console.log("🎉 NEW USER DETECTED - SHOWING WELCOME POPUP!");
          setShowPopup(true);
          setCookie("welcomePopupShown", "true");
        } else {
          console.log("Not a new user, hiding popup");
          setShowPopup(false);
        }
      } catch (error) {
        console.error("Error parsing userData:", error);
        setShowPopup(false);
      }
    } else {
      console.log("Not on profile route, hiding popup");
      setShowPopup(false);
    }
  }, [pathname]);

  // TEMPORARY: Force show popup for testing
  useEffect(() => {
    if (pathname === "/profile") {
      console.log("TEMPORARY OVERRIDE: Forcing popup to show for testing");
      setTimeout(() => {
        setShowPopup(true);
      }, 1000); // Show after 1 second delay
    }
  }, [pathname]);

  function closePopUp() {
    setShowPopup(false);
  }

  function goToMatches() {
    setShowPopup(false);
    router.push("../../matches");
  }

  // If popup shouldn't be shown, return null
  console.log("WelcomeUser render - showPopup:", showPopup);
  if (!showPopup) {
    console.log("WelcomeUser render - returning null (not showing popup)");
    return null;
  }

  console.log("WelcomeUser render - RENDERING POPUP!");

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
      <div className="bg-white flex-col flex items-center px-6 py-8 space-y-8 rounded-2xl shadow-sm max-w-md w-full mx-4">
        <div>
          <Image
            src={Logo}
            width={100}
            height={100}
            className=""
            alt="Pumpkin logo"
          />
        </div>
        <div className="flex items-center flex-col text-center">
          <p className="text-2xl font-bold">Welcome to Pumpkin!</p>
          <p className="text-sm text-gray-700 mt-2">
            Where true love meets fortune. We're excited to have you join our
            community!
          </p>
          <p className="text-sm text-gray-700 mt-4">
            Check out your potential matches and start connecting with people
            who share your interests.
          </p>
        </div>
        <div className="flex w-full flex-col space-y-3">
          <Button
            label={"Go to Matches"}
            variant={"primary"}
            onClick={goToMatches}
          />
          <Button label={"Close"} variant={"secondary"} onClick={closePopUp} />
        </div>
        <div className="text-xs text-gray-500">
          Welcome to your new journey!
        </div>
      </div>
    </div>
  );
}
export default WelcomeUserPopUp;
