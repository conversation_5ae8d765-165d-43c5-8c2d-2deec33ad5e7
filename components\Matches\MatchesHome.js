"use client";
import React, { useEffect, useState } from "react";
import ProfileCard from "../UI/Card/ProfileCard";
import Carousel from "react-multi-carousel";
import "react-multi-carousel/lib/styles.css";
import ProfileCardMatches from "../UI/Card/ProfileCardMatches";

function MatchesHome() {
  const [users, setUsers] = useState([]);

   useEffect(() => {
        fetch('/api/auth')
            .then((res) => res.json())
            .then((data) => {
                setUsers(data)
            })
    }, [])

  const responsive = {
    superLargeDesktop: {
      breakpoint: { max: 4000, min: 3000 },
      items: 1,
    },
    desktop: {
      breakpoint: { max: 3000, min: 1024 },
      items: 1,
    },
    tablet: {
      breakpoint: { max: 1024, min: 464 },
      items: 1,
    },
    mobile: {
      breakpoint: { max: 464, min: 0 },
      items: 1,
    },
  };
  return (
    // Outer container: This flex container ensures its direct child is centered
    <div className="min-h-screen w-full">
      {/* Inner container for the carousel, now acting as the centered content block */}
      <div className="w-full h-auto max-w-[29.4rem] sm:max-w-[16.4rem] md:max-w-[14.1rem] lg:max-w-[19.8rem] xl:max-w-[19.8rem] mx-auto px-4 pt-8 overflow-x-hidden overflow-y-hidden">

        <p
          className="text-xs font-bold text-black"
          style={{ visibility: "hidden" }}
        >
          .
        </p>
        <Carousel
          swipeable={true}
          draggable={true}
          showDots={false}
          infinite={true}
          responsive={responsive}
          containerClass="matches-carousel-container"
          itemClass="px-5"
          
        >
           {users.map((user) => (
            <div key={user._id} className="w-full flex justify-center pb-8">
              <ProfileCardMatches
                id={user._id}
                image={user.profilePicture}
                username={user.username}
                name={user.name}
                surname={user.surname}
                dob={user.dob}
                hickies={user.hickies}
                pumpkins={user.pumpkins}
                email={user.email}
                coverPicture={user.coverPicture}
                city={user.city || ""}
                country={user.country || ""}
              />
            </div>
          ))}
        </Carousel>
      </div>
    </div>
  );
}

export default MatchesHome;